import {
    ActivityIndicator,
    FlatList,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    View,
    TouchableOpacity,
    NativeSyntheticEvent,
    NativeScrollEvent,
    Alert
} from 'react-native'
import DocumentPicker from 'react-native-document-picker'
import ImagePicker from 'react-native-image-crop-picker'
import { createThumbnail } from 'react-native-create-thumbnail'
import React, {
    memo,
    useEffect,
    useState,
    useCallback,
    useRef,
    useMemo
} from 'react'
import {
    Colors,
    Mixins,
    MyText,
    MyWrapper,
    NotifyModal
} from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import IMAGES from '../../assets/images'
import { useSelector, useDispatch } from 'react-redux'
import Clipboard from '@react-native-clipboard/clipboard'
import { requestPermission, openSetting } from '@mwg-kits/core'

import { RootReducerType } from '../../store/reducer'
import { supportTicketActions } from '../../actions'
import { IFromComment, IMessage } from '@types'

import ChatItem from './ChatItem'
import ChatInput from './ChatInput'
import SearchNavigationBar from './SearchNavigationBar'
import {
    ComponentLeft,
    ComponentRight,
    ComponentLeftSearch
} from './HeaderComponents'
import useWebSocket, { getWebSocketStatus } from '../../hooks/useWebSocket'

import DateHeader from './DateHeader'
import TypingIndicator from './TypingIndicator'
import { splitDateTime } from '../../common/helper/dateTimeHelper'

const { translate } = (global as any).props.getTranslateConfig()

interface Props {
    navigation: any
}

const BotSupportTicket = (props: Props) => {
    const dispatch: any = useDispatch()
    const { typeCode, name, profile } = useSelector(
        (state: RootReducerType) => state.commonReducer
    )
    const { getListMessage, searchMessages, getStoreBranch } = useSelector(
        (state: RootReducerType) => state.supportTicketReducer
    )

    const [isSearch, setIsSearch] = useState(false)
    const [isLoadingMore, setIsLoadingMore] = useState(false)
    const [searchText, setSearchText] = useState('')
    const [replyTo, setReplyTo] = useState<IFromComment | undefined>(undefined)
    const [currentLoadedPageIndex, setCurrentLoadedPageIndex] = useState<
        number | null
    >(null)
    const [showScrollToBottom, setShowScrollToBottom] = useState(false)
    const [temporaryHighlightedMessageId, setTemporaryHighlightedMessageId] =
        useState<number | null>(null)
    const [chatInputHeight, setChatInputHeight] = useState(56) // Chiều cao mặc định của ChatInput
    const [currentVisibleDate, setCurrentVisibleDate] = useState<string>('')
    const [isTyping, setIsTyping] = useState(false) // State để hiển thị typing indicator
    const [wsConnectionStatus, setWsConnectionStatus] = useState<{
        connected: boolean
        readyState: number
    }>({ connected: false, readyState: WebSocket.CLOSED })
    const flatListRef = useRef<FlatList>(null)

    const viewabilityConfig = useRef({
        itemVisiblePercentThreshold: 50,
        minimumViewTime: 100
    }).current

    // console.log('firstLog getStoreBranch', getStoreBranch)

    // State cho NotifyModal
    const [errorModalVisible, setErrorModalVisible] = useState(false)
    const [errorModalTitle, setErrorModalTitle] = useState('')
    const [errorModalContent, setErrorModalContent] = useState('')

    // State cho NotifyModal quyền
    const [permissionModalVisible, setPermissionModalVisible] = useState(false)
    const [permissionModalType, setPermissionModalType] = useState<
        'option' | 'confirm' | 'confirm-outline' | 'delete'
    >('option')
    const [permissionType, setPermissionType] = useState<'photo' | 'camera'>(
        'photo'
    )

    // Sử dụng custom hook WebSocket
    const {
        sendMessage: sendWebSocketMessage,
        isConnected: isWebSocketConnected
    } = useWebSocket({
        profileId: profile?.id,
        profile: profile,
        onMessage: (messageData, payloadType) => {
            // Xử lý tin nhắn từ WebSocket
            dispatch(
                supportTicketActions.updateListMessage(messageData, payloadType)
            )

            // Nếu là sự kiện xóa tin nhắn, tải lại danh sách để đảm bảo hiển thị đúng
            if (payloadType === 'delete') {
                console.log('Reloading messages after delete event')
                setTimeout(() => {
                    dispatch(
                        supportTicketActions.getListMessage(
                            {
                                supportServiceCode: typeCode,
                                pageRequest: {
                                    iDisplayStart: 0,
                                    iDisplayLength: 15,
                                    search: ''
                                }
                            },
                            false
                        )
                    )
                }, 500)
            }
        },
        onTypingStatus: (isTypingStatus) => {
            console.log('WebSocket typing status received:', isTypingStatus)
            // Cập nhật state typing indicator
            setIsTyping(isTypingStatus)
        }
    })

    useEffect(() => {
        // Reset danh sách tin nhắn khi chuyển sang tab khác và quay lại
        return () => {
            dispatch(supportTicketActions.resetListMessage())
        }
    }, [dispatch])

    useEffect(() => {
        dispatch(supportTicketActions.getStoreBranch())
    }, [dispatch])

    useEffect(() => {
        console.log("firstLog typeCode", typeCode)

        // Reset danh sách tin nhắn trước khi load data mới để tránh hiển thị tin nhắn của ngành hàng khác
        dispatch(supportTicketActions.resetListMessage())

        // Reset search results để tránh hiển thị kết quả tìm kiếm của ngành hàng khác
        dispatch(supportTicketActions.clearSearchHighlight())

        // Reset các state local liên quan đến search và UI
        setSearchText('')
        setIsSearch(false)
        setReplyTo(undefined)
        setIsTyping(false)

        const data = {
            supportServiceCode: typeCode,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 15,
                search: ''
            }
        }
        dispatch(supportTicketActions.getListMessage(data))
    }, [dispatch, typeCode])

    // Theo dõi trạng thái kết nối WebSocket
    useEffect(() => {
        const checkWebSocketStatus = () => {
            const status = getWebSocketStatus()
            setWsConnectionStatus({
                connected:
                    status.connected && status.readyState === WebSocket.OPEN,
                readyState: status.readyState
            })
        }

        // Kiểm tra trạng thái ngay lập tức
        checkWebSocketStatus()

        // Thiết lập interval để kiểm tra định kỳ
        const statusInterval = setInterval(checkWebSocketStatus, 1000)

        return () => {
            clearInterval(statusInterval)
        }
    }, [])

    // Sắp xếp dữ liệu tin nhắn một lần khi nhận được dữ liệu mới
    const sortedMessages = useMemo(() => {
        return [...getListMessage.data].sort((a, b) => b.id - a.id)
    }, [getListMessage.data])

    // Xử lý dữ liệu tin nhắn để thêm các header ngày
    const processedMessages = useMemo(() => {
        if (sortedMessages.length === 0) return []

        const result: (
            | IMessage
            | { id: string; isDateHeader: true; date: string }
        )[] = []

        // Duyệt qua các tin nhắn đã sắp xếp
        // Vì FlatList đã bị đảo ngược (inverted), nên thêm header ngày sau mỗi nhóm tin nhắn
        for (let i = 0; i < sortedMessages.length; i++) {
            const message = sortedMessages[i]
            const { date } = splitDateTime(message.createTime)

            // Thêm tin nhắn vào kết quả
            result.push(message)

            // Kiểm tra nếu tin nhắn tiếp theo có ngày khác hoặc đây là tin nhắn cuối cùng
            const isLastMessage = i === sortedMessages.length - 1
            const nextMessageHasDifferentDate =
                !isLastMessage &&
                splitDateTime(sortedMessages[i + 1].createTime).date !== date

            // Nếu ngày thay đổi hoặc đây là tin nhắn cuối cùng, thêm header ngày
            if (nextMessageHasDifferentDate || isLastMessage) {
                result.push({
                    id: `date-${date}`,
                    isDateHeader: true,
                    date
                })
            }
        }

        return result
    }, [sortedMessages])

    useEffect(() => {
        if (getListMessage.data.length) {
            const supportChatTicketID =
                getListMessage.data[0].supportChatTicketID

            // Đánh dấu tin nhắn đã đọc
            dispatch(
                supportTicketActions.readComment({
                    supportChatTicketID
                })
            )
        }
    }, [getListMessage.data, profile])

    // Lưu trữ vị trí cuộn hiện tại để khôi phục sau khi tải thêm dữ liệu
    const [scrollOffset, setScrollOffset] = useState(0)

    const handleLoadMore = async () => {
        if (isLoadingMore || !getListMessage.hasMore) return

        try {
            // Lưu vị trí cuộn hiện tại trước khi tải thêm
            if (flatListRef.current) {
                const offset = scrollOffset
                setScrollOffset(offset)
            }

            setIsLoadingMore(true)
            const data = {
                supportServiceCode: typeCode,
                pageRequest: {
                    iDisplayStart: getListMessage.data.length,
                    iDisplayLength: 15,
                    search: searchText
                }
            }
            await dispatch(supportTicketActions.getListMessage(data, false))
        } catch (error) {
            console.error('Failed to load more messages:', error)
        } finally {
            setIsLoadingMore(false)
        }
    }

    const handleSearchSubmit = useCallback(
        (text: string) => {
            if (text.trim() !== '') {
                const supportChatTicketID =
                    getListMessage?.data[0]?.supportChatTicketID || -1

                dispatch(
                    supportTicketActions.searchMessages({
                        supportServiceCode: typeCode,
                        supportChatTicketID: supportChatTicketID,
                        messageID: -1,
                        supportServiceID: -1,
                        actionEvent: null,
                        fromTime: -1,
                        toTime: -1,
                        pageRequest: {
                            iDisplayStart: 0,
                            iDisplayLength: 15,
                            search: text
                        },
                        pageSizeMsg: 15
                    })
                )
            }
            setSearchText(text)
        },
        [dispatch, typeCode, getListMessage?.data]
    )

    // Hàm để cuộn đến tin nhắn dựa vào commentID sử dụng API search
    const scrollToMessage = useCallback(
        async (commentID: number) => {
            if (!commentID || !flatListRef.current) return

            try {
                // Kiểm tra xem tin nhắn đã có trong danh sách hiện tại chưa
                const existingIndex = getListMessage.data.findIndex(
                    (message) => message.id === commentID
                )

                // Nếu tin nhắn đã có trong danh sách, cuộn đến luôn
                if (existingIndex !== -1) {
                    try {
                        // Đặt highlight tạm thời cho tin nhắn
                        setTemporaryHighlightedMessageId(commentID)

                        // Tự động bỏ highlight sau 3 giây
                        setTimeout(() => {
                            setTemporaryHighlightedMessageId(null)
                        }, 3000)

                        flatListRef.current.scrollToIndex({
                            index: existingIndex,
                            animated: true,
                            viewPosition: 0.5,
                            viewOffset: 0
                        })
                        return // Thoát khỏi hàm vì đã tìm thấy tin nhắn
                    } catch (error) {
                        console.error('Lỗi khi cuộn đến tin nhắn:', error)
                    }
                }

                // Nếu chưa có trong danh sách, sử dụng API search để tìm
                const response = await dispatch(
                    supportTicketActions.searchMessageById(commentID)
                )

                if (
                    response?.data?.length > 0 &&
                    response.data[0].arrayPositionMsgFound?.length > 0
                ) {
                    const messagePosition =
                        response.data[0].arrayPositionMsgFound[0]
                    const { pageIndex, position, messageID } = messagePosition

                    console.log(
                        'Tìm thấy tin nhắn tại pageIndex:',
                        pageIndex,
                        'position:',
                        position
                    )

                    // Kiểm tra lại xem tin nhắn đã có trong danh sách sau khi search chưa
                    const messageIndexAfterSearch =
                        getListMessage.data.findIndex(
                            (message) => message.id === messageID
                        )

                    if (messageIndexAfterSearch !== -1) {
                        // Nếu đã có trong danh sách, cuộn đến luôn
                        try {
                            // Đặt highlight tạm thời cho tin nhắn
                            setTemporaryHighlightedMessageId(messageID)

                            // Tự động bỏ highlight sau 3 giây
                            setTimeout(() => {
                                setTemporaryHighlightedMessageId(null)
                            }, 3000)

                            flatListRef.current.scrollToIndex({
                                index: messageIndexAfterSearch,
                                animated: true,
                                viewPosition: 0.5,
                                viewOffset: 0
                            })
                            return
                        } catch (error) {
                            console.error('Lỗi khi cuộn đến tin nhắn:', error)
                        }
                    }

                    // Tải trang chứa tin nhắn nếu cần
                    if (currentLoadedPageIndex !== pageIndex) {
                        // Tính toán vị trí bắt đầu dựa trên pageIndex
                        const startPosition = (pageIndex - 1) * 15

                        await dispatch(
                            supportTicketActions.getListMessage(
                                {
                                    supportServiceCode: typeCode,
                                    pageRequest: {
                                        iDisplayStart: startPosition,
                                        iDisplayLength: 15,
                                        search: ''
                                    }
                                },
                                false
                            )
                        )

                        // Cập nhật pageIndex hiện tại đã tải
                        setCurrentLoadedPageIndex(pageIndex)

                        // Đợi một chút để danh sách cập nhật
                        setTimeout(() => {
                            // Tìm index của tin nhắn trong danh sách hiện tại
                            const messageIndex = getListMessage.data.findIndex(
                                (message) => message.id === messageID
                            )

                            if (messageIndex !== -1) {
                                try {
                                    // Đặt highlight tạm thời cho tin nhắn
                                    setTemporaryHighlightedMessageId(messageID)

                                    // Tự động bỏ highlight sau 3 giây
                                    setTimeout(() => {
                                        setTemporaryHighlightedMessageId(null)
                                    }, 3000)

                                    // Cuộn đến vị trí của tin nhắn
                                    flatListRef.current?.scrollToIndex({
                                        index: messageIndex,
                                        animated: true,
                                        viewPosition: 0.5, // 0.5 để tin nhắn nằm giữa màn hình
                                        viewOffset: 0
                                    })
                                } catch (error) {
                                    console.error(
                                        'Lỗi khi cuộn đến tin nhắn:',
                                        error
                                    )

                                    // Xử lý lỗi khi không thể cuộn đến index
                                    setTimeout(() => {
                                        if (flatListRef.current) {
                                            flatListRef.current.scrollToOffset({
                                                offset: 0,
                                                animated: true
                                            })

                                            setTimeout(() => {
                                                if (flatListRef.current) {
                                                    flatListRef.current.scrollToIndex(
                                                        {
                                                            index: messageIndex,
                                                            animated: true,
                                                            viewPosition: 0.5
                                                        }
                                                    )
                                                }
                                            }, 100)
                                        }
                                    }, 50)
                                }
                            }
                        }, 300)
                    } else {
                        // Nếu đã tải đúng trang, chỉ cần cuộn đến tin nhắn
                        const messageIndex = getListMessage.data.findIndex(
                            (message) => message.id === messageID
                        )

                        if (messageIndex !== -1) {
                            try {
                                // Đặt highlight tạm thời cho tin nhắn
                                setTemporaryHighlightedMessageId(messageID)

                                // Tự động bỏ highlight sau 3 giây
                                setTimeout(() => {
                                    setTemporaryHighlightedMessageId(null)
                                }, 3000)

                                flatListRef.current.scrollToIndex({
                                    index: messageIndex,
                                    animated: true,
                                    viewPosition: 0.5,
                                    viewOffset: 0
                                })
                            } catch (error) {
                                console.error(
                                    'Lỗi khi cuộn đến tin nhắn:',
                                    error
                                )

                                // Xử lý lỗi khi không thể cuộn đến index
                                setTimeout(() => {
                                    if (flatListRef.current) {
                                        const offset = messageIndex * 100 // Ước tính chiều cao trung bình của mỗi tin nhắn
                                        flatListRef.current.scrollToOffset({
                                            offset: offset,
                                            animated: true
                                        })

                                        setTimeout(() => {
                                            if (flatListRef.current) {
                                                flatListRef.current.scrollToIndex(
                                                    {
                                                        index: messageIndex,
                                                        animated: true,
                                                        viewPosition: 0.5
                                                    }
                                                )
                                            }
                                        }, 100)
                                    }
                                }, 50)
                            }
                        }
                    }
                } else {
                    console.log('Không tìm thấy tin nhắn với ID:', commentID)
                }
            } catch (error) {
                console.error('Lỗi khi tìm kiếm tin nhắn:', error)
            }
        },
        [dispatch, typeCode, getListMessage.data, currentLoadedPageIndex]
    )

    const handleReply = useCallback((message: IFromComment) => {
        setReplyTo(message)
    }, [])

    const handleCancelReply = useCallback(() => {
        setReplyTo(undefined)
    }, [])

    const handleCopyMessage = useCallback((content: string) => {
        Clipboard.setString(content)
    }, [])

    const handleDeleteMessage = useCallback(
        async (messageId: number) => {
            try {
                // Thực hiện thu hồi tin nhắn
                console.log('Thu hồi tin nhắn:', messageId)
                await dispatch(supportTicketActions.removeComment(messageId))
                const findMessage = getListMessage.data.find(
                    (message) => message.id === messageId
                )

                // Gửi thông báo xóa tin nhắn qua WebSocket nếu WebSocket đang kết nối
                if (isWebSocketConnected()) {
                    const destination = `TICKET_SIM_SO_USER_${profile?.id}`
                    // Chỉ gửi ID tin nhắn đã xóa
                    const deleteData = {
                        id: findMessage?.id,
                        userId: profile?.id
                    }
                    sendWebSocketMessage(destination, deleteData, 'delete')

                    // Gửi sự kiện typing: false sau khi xóa tin nhắn
                    sendWebSocketMessage(destination, false, 'typing')
                }

                // Tải lại danh sách tin nhắn sau khi thu hồi thành công để đảm bảo hiển thị đúng
                setTimeout(() => {
                    dispatch(
                        supportTicketActions.getListMessage(
                            {
                                supportServiceCode: typeCode,
                                pageRequest: {
                                    iDisplayStart: 0,
                                    iDisplayLength: 15,
                                    search: ''
                                }
                            },
                            false
                        )
                    )
                }, 500)
            } catch (error) {
                console.error('Lỗi khi thu hồi tin nhắn:', error)
            }
        },
        [
            dispatch,
            typeCode,
            getListMessage.data,
            sendWebSocketMessage,
            isWebSocketConnected
        ]
    )

    const renderChatItem = useCallback(
        ({ item }: { item: any }) => {
            // Kiểm tra nếu là header ngày
            if (item.isDateHeader) {
                return <DateHeader date={item.date} />
            }

            // Nếu là tin nhắn thông thường
            return (
                <ChatItem
                    item={item}
                    isCurrentUser={item.creatorUsername === profile?.username}
                    onReply={handleReply}
                    isHighlighted={
                        searchMessages.highlightedMessageId === item.id ||
                        temporaryHighlightedMessageId === item.id
                    }
                    searchText={isSearch ? searchText : ''}
                    onPressReplyPreview={scrollToMessage}
                    onCopyMessage={handleCopyMessage}
                    onDeleteMessage={handleDeleteMessage}
                />
            )
        },
        [
            profile?.username,
            handleReply,
            searchMessages.highlightedMessageId,
            temporaryHighlightedMessageId,
            isSearch,
            searchText,
            scrollToMessage,
            handleCopyMessage,
            handleDeleteMessage
        ]
    )

    const handleSendMessage = useCallback(
        async (message: string) => {
            try {
                const messageRequest = {
                    supportServiceCode: typeCode,
                    message,
                    supportChatTicketID:
                        getListMessage?.data[0]?.supportChatTicketID || -1,
                    ...(replyTo ? { fromMessage: replyTo } : {})
                }

                // Gửi tin nhắn và lấy response
                const response = await dispatch(
                    supportTicketActions.sendMessage(messageRequest)
                )

                // Lưu ID tin nhắn đã gửi để tránh hiển thị trùng lặp từ WebSocket
                if (response && response.id) {
                    console.log('Sent message ID:', response.id)

                    // Gửi tin nhắn qua WebSocket cho người nhận nếu WebSocket đang kết nối
                    if (isWebSocketConnected()) {
                        const destination = `TICKET_SIM_SO_USER_${profile?.id}`
                        sendWebSocketMessage(destination, response, 'message')

                        // Gửi sự kiện typing: false sau khi gửi tin nhắn
                        sendWebSocketMessage(destination, false, 'typing')
                    }
                }

                setReplyTo(undefined)

                // Lấy tin nhắn mới nhất sau khi gửi
                // Sử dụng timeout để tránh race condition với WebSocket
                setTimeout(async () => {
                    await dispatch(
                        supportTicketActions.getListMessage(
                            {
                                supportServiceCode: typeCode,
                                pageRequest: {
                                    iDisplayStart: 0,
                                    iDisplayLength: 15,
                                    search: ''
                                }
                            },
                            false
                        )
                    )

                    // Cuộn xuống dưới cùng sau khi gửi tin nhắn
                    if (flatListRef.current) {
                        flatListRef.current?.scrollToOffset({
                            offset: 0,
                            animated: true
                        })
                    }
                }, 500)
            } catch (error) {
                console.error('Failed to send message:', error)
            }
        },
        [
            dispatch,
            typeCode,
            getListMessage.data,
            replyTo,
            sendWebSocketMessage,
            isWebSocketConnected
        ]
    )

    const handleToggleSearch = useCallback(() => {
        // Nếu đang ở chế độ tìm kiếm và chuyển sang chế độ bình thường
        // thì đặt lại trạng thái highlight
        if (isSearch) {
            dispatch(supportTicketActions.clearSearchHighlight())
            setSearchText('')
        }
        setIsSearch(!isSearch)
    }, [isSearch, dispatch])

    const handleNavigateSearchResult = useCallback(
        (direction: 'next' | 'prev') => {
            // Gọi action để chuyển đến kết quả tìm kiếm tiếp theo hoặc trước đó
            dispatch(supportTicketActions.navigateSearchResult(direction))
        },
        [dispatch]
    )

    const handleGoBack = useCallback(() => {
        props.navigation.goBack()
    }, [props.navigation])

    const handleClearSearch = useCallback(() => {
        setSearchText('')
        // Đặt lại trạng thái highlight khi hủy tìm kiếm
        dispatch(supportTicketActions.clearSearchHighlight())
        dispatch(
            supportTicketActions.getListMessage({
                supportServiceCode: typeCode,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 15,
                    search: ''
                }
            })
        )
    }, [dispatch, typeCode])

    const handleScrollToBottom = useCallback(() => {
        if (flatListRef.current) {
            flatListRef.current.scrollToOffset({ offset: 0, animated: true })
        }
    }, [])

    // Xử lý sự kiện typing
    const handleTyping = useCallback(
        (isTyping: boolean) => {
            // Kiểm tra trạng thái kết nối WebSocket trước khi gửi
            if (!isWebSocketConnected()) {
                console.log(
                    'WebSocket not connected or permanently closed, skipping typing event'
                )
                return
            }

            // Gửi thông báo typing qua WebSocket
            const destination = `TICKET_SIM_SO_USER_${profile?.id}`
            // Gửi trạng thái typing (true hoặc false)
            sendWebSocketMessage(destination, isTyping, 'typing')
        },
        [profile, sendWebSocketMessage, isWebSocketConnected]
    )

    const handleSelectFile = useCallback(async () => {
        try {
            // Kiểm tra quyền truy cập storage
            try {
                await requestPermission('storage')
            } catch (permissionError) {
                console.log('Permission error:', permissionError)
                // Hiển thị thông báo yêu cầu quyền
                setPermissionType('photo') // Sử dụng photo vì storage cũng liên quan đến quyền đọc file
                setPermissionModalType('option')
                setPermissionModalVisible(true)
                return
            }

            // Mở document picker để chọn file, loại trừ các file hình ảnh và video
            const result = await DocumentPicker.pick({
                type: [
                    DocumentPicker.types.pdf,
                    DocumentPicker.types.doc,
                    DocumentPicker.types.docx,
                    DocumentPicker.types.xls,
                    DocumentPicker.types.xlsx,
                    DocumentPicker.types.ppt,
                    DocumentPicker.types.pptx,
                    DocumentPicker.types.zip,
                    DocumentPicker.types.plainText
                ],
                allowMultiSelection: false
            })

            if (result && result.length > 0) {
                const file = result[0]
                console.log('File đã chọn:', file)

                // Gửi file mà không cần poster
                const response = await dispatch(
                    supportTicketActions.attachFile(file)
                )

                // Gửi thông báo qua WebSocket
                if (response && isWebSocketConnected()) {
                    const destination = `TICKET_SIM_SO_USER_${profile?.id}`
                    sendWebSocketMessage(destination, response, 'message')
                }

                // Tải lại danh sách tin nhắn sau khi gửi file thành công
                await dispatch(
                    supportTicketActions.getListMessage(
                        {
                            supportServiceCode: typeCode,
                            pageRequest: {
                                iDisplayStart: 0,
                                iDisplayLength: 15,
                                search: ''
                            }
                        },
                        false
                    )
                )

                // Cuộn xuống dưới cùng
                setTimeout(() => {
                    if (flatListRef.current) {
                        flatListRef.current.scrollToOffset({
                            offset: 0,
                            animated: true
                        })
                    }
                }, 300)
            }
        } catch (err) {
            if (DocumentPicker.isCancel(err)) {
                // Người dùng đã hủy việc chọn file
                console.log('Đã hủy chọn file')
            } else {
                // Xảy ra lỗi khác
                console.error('Lỗi khi chọn file:', err)
                setErrorModalTitle('Lỗi')
                setErrorModalContent(
                    'Không thể tải lên file. Vui lòng thử lại sau.'
                )
                setErrorModalVisible(true)
            }
        }
    }, [
        dispatch,
        setPermissionType,
        setPermissionModalType,
        setPermissionModalVisible,
        typeCode,
        profile,
        sendWebSocketMessage,
        isWebSocketConnected
    ])

    // Hàm hiển thị tiến trình tải lên (không hiển thị gì)
    const showUploadProgress = (_current: number, _total: number) => {
        // Không hiển thị gì cả
        // Thêm dấu gạch dưới trước tên tham số để tránh cảnh báo về tham số không sử dụng
    }

    const handleSelectImage = useCallback(async () => {
        try {
            // Kiểm tra quyền truy cập thư viện ảnh
            try {
                await requestPermission('photo')
            } catch (permissionError) {
                console.log('Permission error:', permissionError)
                // Hiển thị thông báo yêu cầu quyền
                setPermissionType('photo')
                setPermissionModalType('option')
                setPermissionModalVisible(true)
                return
            }

            // Mở image picker để chọn nhiều hình ảnh
            const images = await ImagePicker.openPicker({
                width: 1000,
                height: 1000,
                cropping: false,
                mediaType: 'photo',
                multiple: true,
                maxFiles: 10 // Giới hạn số lượng file có thể chọn
            })

            // Không hiển thị log

            if (!images || images.length === 0) {
                return
            }

            // Không hiển thị thông báo đang tải

            // Tạo mảng file objects từ images
            const files = images.map((image) => ({
                uri: image.path,
                type: image.mime,
                name: image.path.split('/').pop() || 'image.jpg'
            }))

            // Biến để theo dõi tiến trình
            let uploadedCount = 0
            let successCount = 0

            // Gửi từng ảnh một, tuần tự
            for (let i = 0; i < files.length; i++) {
                const file = files[i]
                try {
                    // Gửi ảnh lên server
                    const response = await dispatch(
                        supportTicketActions.attachFile(file)
                    )
                    uploadedCount++
                    successCount++

                    // Gửi thông báo qua WebSocket nếu WebSocket đang kết nối
                    if (response && isWebSocketConnected()) {
                        const destination = `TICKET_SIM_SO_USER_${profile?.id}`
                        sendWebSocketMessage(destination, response, 'message')
                    }

                    // Hiển thị tiến trình
                    showUploadProgress(uploadedCount, files.length)
                    // Không hiển thị log
                } catch (error) {
                    uploadedCount++
                    // Không hiển thị log lỗi

                    // Hiển thị tiến trình
                    showUploadProgress(uploadedCount, files.length)
                }
            }

            // Chỉ thông báo khi có lỗi
            if (successCount > 0) {
                // Nếu có ảnh không tải được, hiển thị thông báo lỗi
                if (successCount < files.length) {
                    setErrorModalTitle('Lỗi')
                    setErrorModalContent(
                        `Có ${
                            files.length - successCount
                        } ảnh không thể tải lên.`
                    )
                    setErrorModalVisible(true)
                }

                // Tải lại danh sách tin nhắn sau khi tất cả ảnh đã được gửi
                await dispatch(
                    supportTicketActions.getListMessage(
                        {
                            supportServiceCode: typeCode,
                            pageRequest: {
                                iDisplayStart: 0,
                                iDisplayLength: 15,
                                search: ''
                            }
                        },
                        false
                    )
                )

                // Cuộn xuống dưới cùng
                setTimeout(() => {
                    if (flatListRef.current) {
                        flatListRef.current.scrollToOffset({
                            offset: 0,
                            animated: true
                        })
                    }
                }, 300)
            } else {
                setErrorModalTitle('Lỗi')
                setErrorModalContent(
                    'Không thể tải lên hình ảnh. Vui lòng thử lại sau.'
                )
                setErrorModalVisible(true)
            }
        } catch (err: any) {
            if (err?.toString().includes('User cancelled image selection')) {
                // Không hiển thị log
            } else {
                // Không hiển thị log lỗi
                setErrorModalTitle('Lỗi')
                setErrorModalContent(
                    'Không thể tải lên hình ảnh. Vui lòng thử lại sau.'
                )
                setErrorModalVisible(true)
            }
        }
    }, [
        dispatch,
        typeCode,
        flatListRef,
        setPermissionType,
        setPermissionModalType,
        setPermissionModalVisible,
        getListMessage?.data,
        sendWebSocketMessage,
        isWebSocketConnected
    ])

    const handleSelectMediaGallery = useCallback(async () => {
        try {
            // Kiểm tra quyền truy cập thư viện ảnh
            try {
                await requestPermission('photo')
            } catch (permissionError) {
                console.log('Permission error:', permissionError)
                // Hiển thị thông báo yêu cầu quyền
                setPermissionType('photo')
                setPermissionModalType('option')
                setPermissionModalVisible(true)
                return
            }

            // Mở image picker để chọn nhiều hình ảnh và video
            const media = await ImagePicker.openPicker({
                width: 1000,
                height: 1000,
                cropping: false,
                mediaType: 'any',
                multiple: true,
                maxFiles: 10 // Giới hạn số lượng file có thể chọn
            })

            if (!media || media.length === 0) {
                return
            }

            // Tạo mảng file objects từ media
            const files = media.map((item) => ({
                uri: item.path,
                type: item.mime,
                name:
                    item.path.split('/').pop() ||
                    (item.mime.startsWith('image/') ? 'image.jpg' : 'video.mp4')
            }))

            // Biến để theo dõi tiến trình
            let uploadedCount = 0
            let successCount = 0

            // Gửi từng file một, tuần tự
            for (let i = 0; i < files.length; i++) {
                const file = files[i]
                try {
                    // Kiểm tra nếu là file video, tự động tạo thumbnail
                    if (file.type && file.type.startsWith('video/')) {
                        let thumbnailFile = null

                        const pathIOS = file.uri.replace('file://', '')

                        // Cố gắng tạo thumbnail trước
                        try {
                            // Tạo thumbnail từ video
                            const thumbnail = await createThumbnail({
                                url: Platform.OS === 'ios' ? pathIOS : file.uri,
                                timeStamp: 100,
                                maxHeight: 300,
                                maxWidth: 300,
                                onlySyncedFrames: false,
                                format: 'jpeg'
                            })

                            // Tạo file object từ thumbnail
                            thumbnailFile = {
                                uri: thumbnail.path,
                                type: 'image/jpeg',
                                name: 'thumbnail.jpg'
                            }
                        } catch (err) {
                            console.log('Lỗi khi tạo thumbnail:', err)
                            // Nếu có lỗi khi tạo thumbnail, thumbnailFile vẫn là null
                        }

                        // Gửi file video với hoặc không có thumbnail
                        try {
                            const response = await dispatch(
                                supportTicketActions.attachFile(
                                    file,
                                    thumbnailFile
                                )
                            )
                            uploadedCount++
                            successCount++

                            // Gửi thông báo qua WebSocket nếu WebSocket đang kết nối
                            if (response && isWebSocketConnected()) {
                                const destination = `TICKET_SIM_SO_USER_${profile?.id}`
                                sendWebSocketMessage(
                                    destination,
                                    response,
                                    'message'
                                )
                            }
                        } catch (uploadError) {
                            console.log('Lỗi khi gửi file video:', uploadError)
                            uploadedCount++
                            // Không tăng successCount vì upload thất bại
                        }

                        // Hiển thị tiến trình
                        showUploadProgress(uploadedCount, files.length)
                    } else {
                        // Nếu không phải file video, gửi file mà không cần poster
                        const response = await dispatch(
                            supportTicketActions.attachFile(file)
                        )
                        uploadedCount++
                        successCount++

                        // Gửi thông báo qua WebSocket nếu WebSocket đang kết nối
                        if (response && isWebSocketConnected()) {
                            const destination = `TICKET_SIM_SO_USER_${profile?.id}`
                            sendWebSocketMessage(
                                destination,
                                response,
                                'message'
                            )
                        }

                        // Hiển thị tiến trình
                        showUploadProgress(uploadedCount, files.length)
                    }
                } catch (error) {
                    uploadedCount++
                    // Không hiển thị log lỗi

                    // Hiển thị tiến trình
                    showUploadProgress(uploadedCount, files.length)
                }
            }

            // Chỉ thông báo khi có lỗi
            if (successCount > 0) {
                // Nếu có file không tải được, hiển thị thông báo lỗi
                if (successCount < files.length) {
                    setErrorModalTitle('Lỗi')
                    setErrorModalContent(
                        `Có ${
                            files.length - successCount
                        } file không thể tải lên.`
                    )
                    setErrorModalVisible(true)
                }

                // Tải lại danh sách tin nhắn sau khi tất cả file đã được gửi
                await dispatch(
                    supportTicketActions.getListMessage(
                        {
                            supportServiceCode: typeCode,
                            pageRequest: {
                                iDisplayStart: 0,
                                iDisplayLength: 15,
                                search: ''
                            }
                        },
                        false
                    )
                )

                // Cuộn xuống dưới cùng
                setTimeout(() => {
                    if (flatListRef.current) {
                        flatListRef.current.scrollToOffset({
                            offset: 0,
                            animated: true
                        })
                    }
                }, 300)
            } else {
                setErrorModalTitle('Lỗi')
                setErrorModalContent(
                    'Không thể tải lên file. Vui lòng thử lại sau.'
                )
                setErrorModalVisible(true)
            }
        } catch (err: any) {
            if (err?.toString().includes('User cancelled image selection')) {
                // Không hiển thị log
            } else {
                // Không hiển thị log lỗi
                setErrorModalTitle('Lỗi')
                setErrorModalContent(
                    'Không thể tải lên file. Vui lòng thử lại sau.'
                )
                setErrorModalVisible(true)
            }
        }
    }, [
        dispatch,
        typeCode,
        flatListRef,
        setPermissionType,
        setPermissionModalType,
        setPermissionModalVisible,
        getListMessage?.data,
        sendWebSocketMessage,
        isWebSocketConnected
    ])

    // Callback khi các item hiển thị thay đổi
    const onViewableItemsChanged = useCallback(
        ({ viewableItems }: { viewableItems: Array<any> }) => {
            if (viewableItems.length > 0) {
                // Tìm header ngày trong danh sách các item đang hiển thị
                const dateHeader = viewableItems.find(
                    (item: any) =>
                        item.item &&
                        'isDateHeader' in item.item &&
                        item.item.isDateHeader
                )

                if (
                    dateHeader &&
                    dateHeader.item &&
                    'date' in dateHeader.item
                ) {
                    // Chỉ cập nhật ngày hiển thị khi tìm thấy header ngày
                    if (dateHeader.item.date !== currentVisibleDate) {
                        setCurrentVisibleDate(dateHeader.item.date)
                    }
                } else {
                    // Nếu không tìm thấy header ngày, tìm tin nhắn đầu tiên
                    const firstMessage = viewableItems.find(
                        (item: any) =>
                            item.item &&
                            !('isDateHeader' in item.item) &&
                            item.item.createTime
                    )

                    if (firstMessage && firstMessage.item) {
                        // Tìm header ngày gần nhất trong danh sách đã xử lý
                        // Chỉ hiển thị header ngày khi có sự thay đổi ngày
                        const messageId = firstMessage.item.id

                        // Tìm vị trí của tin nhắn trong danh sách đã xử lý
                        const messageIndex = processedMessages.findIndex(
                            (msg) =>
                                !('isDateHeader' in msg) && msg.id === messageId
                        )

                        if (messageIndex !== -1) {
                            // Tìm header ngày gần nhất sau tin nhắn này
                            let nearestDateHeader = null
                            for (
                                let i = messageIndex + 1;
                                i < processedMessages.length;
                                i++
                            ) {
                                const msg = processedMessages[i]
                                if ('isDateHeader' in msg && msg.isDateHeader) {
                                    nearestDateHeader = msg
                                    break
                                }
                            }

                            if (
                                nearestDateHeader &&
                                'date' in nearestDateHeader &&
                                nearestDateHeader.date !== currentVisibleDate
                            ) {
                                setCurrentVisibleDate(nearestDateHeader.date)
                            }
                        }
                    }
                }
            }
        },
        [currentVisibleDate, processedMessages]
    )

    // Ref cho viewabilityConfigCallbackPairs
    const viewabilityConfigCallbackPairs = useRef([
        {
            viewabilityConfig: {
                itemVisiblePercentThreshold: 50,
                minimumViewTime: 100
            },
            onViewableItemsChanged: onViewableItemsChanged
        }
    ])

    const handleScroll = useCallback(
        (event: NativeSyntheticEvent<NativeScrollEvent>) => {
            // Vì FlatList đã được inverted, nên offset.y > 0 nghĩa là đang scroll lên
            const offsetY = event.nativeEvent.contentOffset.y

            // Hiển thị nút khi scroll lên (offset > 0)
            if (offsetY > 100 && !showScrollToBottom) {
                setShowScrollToBottom(true)
            } else if (offsetY <= 100 && showScrollToBottom) {
                setShowScrollToBottom(false)
            }
        },
        [showScrollToBottom]
    )

    const loadMessagesFromSearch = useCallback(
        (pageIndex: number) => {
            // Kiểm tra xem tin nhắn của trang này đã có trong danh sách chưa
            // Tính vị trí bắt đầu và kết thúc của trang
            const startPosition = (pageIndex - 1) * 15
            const endPosition = startPosition + 14 // 15 tin nhắn mỗi trang (0-14)

            // Kiểm tra xem đã có tin nhắn nào trong khoảng này chưa
            const hasMessagesInRange = getListMessage.data.some((message) => {
                // Tìm vị trí của tin nhắn trong tổng số tin nhắn
                const messagePosition =
                    getListMessage.data.length -
                    getListMessage.data.indexOf(message) -
                    1
                return (
                    messagePosition >= startPosition &&
                    messagePosition <= endPosition
                )
            })

            // Chỉ tải lại trang nếu chưa có tin nhắn nào trong khoảng này
            if (!hasMessagesInRange) {
                dispatch(
                    supportTicketActions.getListMessage(
                        {
                            supportServiceCode: typeCode,
                            pageRequest: {
                                iDisplayStart: startPosition,
                                iDisplayLength: 15,
                                search: ''
                            }
                        },
                        false
                    )
                )
            }

            // Cập nhật pageIndex hiện tại đã tải
            setCurrentLoadedPageIndex(pageIndex)
        },
        [dispatch, typeCode, getListMessage.data, currentLoadedPageIndex]
    )

    const scrollToHighlightedMessage = useCallback(() => {
        if (!searchMessages.highlightedMessageId || !flatListRef.current) return

        // Tìm index của tin nhắn được highlight trong danh sách hiện tại
        const messageIndex = getListMessage.data.findIndex(
            (message) => message.id === searchMessages.highlightedMessageId
        )

        if (messageIndex !== -1) {
            console.log('Tìm thấy tin nhắn tại index:', messageIndex)

            try {
                // Cuộn đến vị trí của tin nhắn được highlight
                flatListRef.current.scrollToIndex({
                    index: messageIndex,
                    animated: true,
                    viewPosition: 0.5, // 0.5 để tin nhắn nằm giữa màn hình
                    viewOffset: 0
                })
            } catch (error) {
                console.error('Lỗi khi cuộn đến tin nhắn:', error)

                // Nếu không thể cuộn đến index, thử cuộn đến đầu danh sách
                setTimeout(() => {
                    if (flatListRef.current) {
                        flatListRef.current.scrollToOffset({
                            offset: 0,
                            animated: true
                        })

                        // Thử lại sau một khoảng thời gian
                        setTimeout(() => {
                            if (flatListRef.current) {
                                flatListRef.current.scrollToIndex({
                                    index: messageIndex,
                                    animated: true,
                                    viewPosition: 0.5
                                })
                            }
                        }, 100)
                    }
                }, 50)
            }
        }
    }, [searchMessages.highlightedMessageId, getListMessage.data])

    // Khi highlightedMessageId thay đổi, tải tin nhắn và cuộn đến vị trí mới

    useEffect(() => {
        if (
            searchMessages.foundPositions.length > 0 &&
            searchMessages.highlightedMessageId
        ) {
            // Lấy vị trí hiện tại trong kết quả tìm kiếm
            const currentPosition =
                searchMessages.foundPositions[searchMessages.currentSearchIndex]

            // Tải tin nhắn từ trang chứa kết quả tìm kiếm
            loadMessagesFromSearch(currentPosition.pageIndex)

            // Cuộn đến tin nhắn được tìm thấy ngay sau khi tải
            setTimeout(() => {
                scrollToHighlightedMessage()
            }, 100)
        }
    }, [
        searchMessages.foundPositions,
        searchMessages.highlightedMessageId,
        searchMessages.currentSearchIndex,
        loadMessagesFromSearch,
        scrollToHighlightedMessage
    ])

    useEffect(() => {
        if (!isSearch) {
            setCurrentLoadedPageIndex(null)
        }
    }, [isSearch])

    // Theo dõi thay đổi chiều cao của ChatInput để cập nhật lại vị trí cuộn
    useEffect(() => {
        if (flatListRef.current && !isSearch) {
            // Đợi một chút để FlatList cập nhật lại kích thước
            setTimeout(() => {
                // Giữ nguyên vị trí cuộn hiện tại
                flatListRef.current?.scrollToOffset({
                    offset: scrollOffset,
                    animated: false
                })
            }, 50)
        }
    }, [chatInputHeight, isSearch, scrollOffset])

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}>
            <MyWrapper
                isSuccess={getListMessage.error.code === 0}
                isError={getListMessage.error.code !== 0}
                isLoading={getListMessage.loading}
                messageError={getListMessage.error.errorReason || ''}
                actionRetry={() => {
                    dispatch(
                        supportTicketActions.getListMessage({
                            supportServiceCode: typeCode,
                            pageRequest: {
                                iDisplayStart: 0,
                                iDisplayLength: 15,
                                search: searchText
                            }
                        })
                    )
                }}
                useKeyboardAwareScrollViewWrap={false}
                navigation={props.navigation}
                componentsLeftTitle={isSearch ? '' : name}
                isErrorIcon={{ uri: 'ic_error' }}
                buttonRetry={translate('retry')}
                componentsRight={() =>
                    !isSearch && (
                        <ComponentRight
                            onToggleSearch={handleToggleSearch}
                            wsConnectionStatus={wsConnectionStatus}
                        />
                    )
                }
                componentsLeft={() =>
                    isSearch ? (
                        <ComponentLeftSearch
                            searchText={searchText}
                            onToggleSearch={handleToggleSearch}
                            onSearchSubmit={handleSearchSubmit}
                            onClearSearch={handleClearSearch}
                        />
                    ) : (
                        <ComponentLeft name={name} onGoBack={handleGoBack} />
                    )
                }>
                <View
                    style={[
                        styles.container,
                        { paddingBottom: chatInputHeight + Mixins.scale(32) }
                    ]}>
                    <View style={{ flex: 1 }}>
                        {processedMessages.length === 0 ? (
                            <View style={styles.emptyContainer}>
                                <MyText
                                    category="body.1"
                                    text={
                                        isSearch
                                            ? 'Không tìm thấy tin nhắn'
                                            : 'Chưa có tin nhắn'
                                    }
                                />
                            </View>
                        ) : (
                            <FlatList
                                ref={flatListRef}
                                showsVerticalScrollIndicator={false}
                                data={processedMessages}
                                renderItem={renderChatItem}
                                keyExtractor={(item) => item.id.toString()}
                                contentContainerStyle={[
                                    styles.listContentContainer,
                                    {
                                        paddingBottom:
                                            chatInputHeight + Mixins.scale(16)
                                    }
                                ]}
                                onEndReached={handleLoadMore}
                                onEndReachedThreshold={0.5}
                                inverted={true}
                                onScroll={handleScroll}
                                scrollEventThrottle={16}
                                viewabilityConfigCallbackPairs={
                                    viewabilityConfigCallbackPairs.current
                                }
                                maintainVisibleContentPosition={{
                                    minIndexForVisible: 0,
                                    autoscrollToTopThreshold: 10
                                }}
                                onMomentumScrollEnd={(event) => {
                                    // Lưu vị trí cuộn khi dừng cuộn
                                    setScrollOffset(
                                        event.nativeEvent.contentOffset.y
                                    )
                                }}
                                style={{
                                    paddingHorizontal: Mixins.scale(16)
                                }}
                                scrollEnabled={true}
                                directionalLockEnabled={true}
                                disableScrollViewPanResponder={false}
                                scrollsToTop={false}
                                removeClippedSubviews={true}
                                windowSize={10}
                                maxToRenderPerBatch={15}
                                updateCellsBatchingPeriod={30}
                                initialNumToRender={15}
                                getItemLayout={(_, index) => ({
                                    length: 100, // Ước tính chiều cao trung bình của mỗi tin nhắn
                                    offset: 100 * index,
                                    index
                                })}
                                viewabilityConfig={viewabilityConfig}
                                onScrollToIndexFailed={(info) => {
                                    // Xử lý khi không thể cuộn đến index
                                    console.log(
                                        'Không thể cuộn đến index:',
                                        info
                                    )
                                    // Thử lại sau một khoảng thời gian
                                    setTimeout(() => {
                                        if (flatListRef.current) {
                                            // Tính toán vị trí gần đúng và cuộn đến đó
                                            const offset =
                                                info.averageItemLength *
                                                info.index
                                            flatListRef.current.scrollToOffset({
                                                offset: offset,
                                                animated: true
                                            })

                                            // Sau khi cuộn đến gần vị trí, thử lại scrollToIndex
                                            setTimeout(() => {
                                                if (flatListRef.current) {
                                                    flatListRef.current.scrollToIndex(
                                                        {
                                                            index: info.index,
                                                            animated: true,
                                                            viewPosition: 0.5
                                                        }
                                                    )
                                                }
                                            }, 50)
                                        }
                                    }, 100)
                                }}
                                // ListEmptyComponent={() => (

                                // )}
                                ListHeaderComponent={
                                    <>
                                        {isLoadingMore ? (
                                            <View
                                                style={styles.loadingContainer}>
                                                <ActivityIndicator
                                                    size="small"
                                                    color={
                                                        Colors.BADGE_SOLID_BLUE_BG
                                                    }
                                                />
                                            </View>
                                        ) : null}
                                        {/* Hiển thị typing indicator khi nhận được sự kiện typing từ WebSocket */}
                                        {!isSearch && (
                                            <TypingIndicator
                                                isVisible={isTyping}
                                            />
                                        )}
                                    </>
                                }
                            />
                        )}
                    </View>

                    {showScrollToBottom && !isSearch && (
                        <TouchableOpacity
                            style={[
                                styles.scrollToBottomButton,
                                {
                                    bottom: chatInputHeight + Mixins.scale(40)
                                }
                            ]}
                            onPress={handleScrollToBottom}>
                            <Image
                                //@ts-ignore
                                isLocal
                                source={IMAGES.ic_chevron_down}
                                style={styles.scrollToBottomIcon}
                            />
                        </TouchableOpacity>
                    )}

                    {isSearch ? (
                        <View style={styles.searchNavigationContainer}>
                            <SearchNavigationBar
                                currentIndex={searchMessages.currentSearchIndex}
                                totalResults={
                                    searchMessages.foundPositions.length
                                }
                                onPrevResult={() =>
                                    handleNavigateSearchResult('prev')
                                }
                                onNextResult={() =>
                                    handleNavigateSearchResult('next')
                                }
                                searchText={searchText}
                            />
                        </View>
                    ) : (
                        <View style={styles.chatInputContainer}>
                            <ChatInput
                                onSendMessage={handleSendMessage}
                                replyTo={replyTo}
                                onCancelReply={handleCancelReply}
                                onSelectFile={handleSelectFile}
                                onSelectMediaGallery={handleSelectMediaGallery}
                                onHeightChange={setChatInputHeight}
                                onTyping={handleTyping}
                            />
                        </View>
                    )}
                </View>
            </MyWrapper>

            {/* NotifyModal cho thông báo lỗi */}
            <NotifyModal
                isVisible={errorModalVisible}
                onCancel={() => setErrorModalVisible(false)}
                title={errorModalTitle}
                content={errorModalContent}
                typeNotify="confirm"
                typeIcon="error"
                titleConfirm="Đồng ý"
                onConfirm={() => setErrorModalVisible(false)}
            />

            {/* NotifyModal cho thông báo quyền */}
            <NotifyModal
                isVisible={permissionModalVisible}
                onCancel={() => {
                    setPermissionModalVisible(false)
                }}
                title="Yêu cầu quyền truy cập"
                content={
                    permissionType === 'photo'
                        ? 'Ứng dụng cần quyền truy cập vào thư viện ảnh để gửi hình ảnh.'
                        : 'Ứng dụng cần quyền truy cập vào camera để chụp ảnh.'
                }
                typeNotify={permissionModalType}
                typeIcon="info"
                titleCancel="Hủy"
                titleConfirm="Cài đặt"
                onConfirm={() => {
                    setPermissionModalVisible(false)
                    openSetting()
                }}
            />
        </KeyboardAvoidingView>
    )
}

const styles = StyleSheet.create({
    titleLeft: {
        color: Colors.TEXT_PRIMARY,
        maxWidth: Mixins.scale(300),
        fontSize: Mixins.scaleFont(20),
        ...(Platform.OS === 'android' ? { fontWeight: '700' } : {})
    },
    container: {
        flex: 1,
        position: 'relative',
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY
        // Đã loại bỏ paddingBottom mặc định, sẽ điều chỉnh động theo chiều cao của ChatInput
    },
    listContentContainer: {
        gap: Mixins.scale(16),
        paddingTop: Mixins.scale(16),
        paddingBottom: Mixins.scale(16),
        flexGrow: 1,
        justifyContent: 'flex-end'
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: Mixins.scale(16)
    },
    loadingContainer: {
        padding: Mixins.scale(16),
        alignItems: 'center'
    },
    chatInputContainer: {
        position: 'absolute',
        bottom: 0,
        alignSelf: 'center',
        width: '100%',
        paddingTop: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(16),
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        zIndex: 10 // Đảm bảo hiển thị trên cùng
    },
    searchNavigationContainer: {
        position: 'absolute',
        bottom: 0,
        alignSelf: 'center',
        width: '100%',
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        zIndex: 10 // Đảm bảo hiển thị trên cùng
    },
    searchLoadingContainer: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -75 }, { translateY: -20 }],
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        padding: Mixins.scale(10),
        borderRadius: Mixins.scale(8),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        width: Mixins.scale(150)
    },
    scrollToBottomButton: {
        position: 'absolute',
        // bottom: Mixins.scale(80),
        right: Mixins.scale(16),
        width: Mixins.scale(40),
        height: Mixins.scale(40),
        borderRadius: Mixins.scale(20),
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        zIndex: 10
    },
    scrollToBottomIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        tintColor: Colors.ICON_NEUTRALS_PRIMARY
    }
})

export default memo(BotSupportTicket)
